#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vbee Auto Login Tool
Tool tự động đăng nhập vào hệ thống Vbee và lấy JWT Bearer Token

Author: AI Assistant
Created: 2025-07-29
"""

import requests
import json
import time
import logging
import hashlib
import base64
import secrets
import urllib.parse
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import re

class VbeeAutoLogin:
    def __init__(self, config_file: str = "config.json"):
        """
        Khởi tạo Vbee Auto Login Tool
        
        Args:
            config_file: Đường dẫn file config
        """
        self.config = self.load_config(config_file)
        self.session = requests.Session()
        self.setup_logging()
        self.setup_session()
        
        # OAuth PKCE parameters
        self.code_verifier = None
        self.code_challenge = None
        self.session_id = None
        
    def load_config(self, config_file: str) -> Dict:
        """
        <PERSON><PERSON><PERSON> file config, tạo mới nếu không tồn tại
        """
        default_config = {
            "timeout": 30,
            "retry_count": 3,
            "delay_between_requests": 2,
            "delay_between_accounts": 5,
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "client_id": "aivoice-web-application",
            "redirect_uri": "https://studio.vbee.vn/studio/text-to-speech?",
            "base_urls": {
                "auth": "https://auth.vbee.vn",
                "accounts": "https://accounts.vbee.vn",
                "studio": "https://studio.vbee.vn"
            }
        }
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # Merge với default config
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except FileNotFoundError:
            # Tạo file config mới
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            return default_config
    
    def setup_logging(self):
        """
        Thiết lập logging system
        """
        # Tạo logger
        self.logger = logging.getLogger('VbeeAutoLogin')
        self.logger.setLevel(logging.INFO)
        
        # Tạo formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # File handler
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_handler = logging.FileHandler(f'vbee_login_{timestamp}.log', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Thêm handlers
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def setup_session(self):
        """
        Thiết lập HTTP session với headers cần thiết
        """
        self.session.headers.update({
            'User-Agent': self.config['user_agent'],
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site'
        })
        
        # Thiết lập timeout
        self.session.timeout = self.config['timeout']
    
    def generate_pkce_params(self):
        """
        Tạo PKCE parameters cho OAuth flow
        """
        # Tạo code verifier (43-128 characters)
        self.code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        
        # Tạo code challenge
        challenge_bytes = hashlib.sha256(self.code_verifier.encode('utf-8')).digest()
        self.code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
        
        self.logger.debug(f"Generated PKCE - Verifier: {self.code_verifier[:20]}...")
        self.logger.debug(f"Generated PKCE - Challenge: {self.code_challenge}")
    
    def start_oauth_flow(self) -> bool:
        """
        Bước 1: Khởi tạo OAuth authorization flow
        
        Returns:
            bool: True nếu thành công, False nếu thất bại
        """
        try:
            self.logger.info("🚀 Bắt đầu OAuth authorization flow...")
            
            # Tạo PKCE parameters
            self.generate_pkce_params()
            
            # Tạo authorize URL
            params = {
                'clientId': self.config['client_id'],
                'codeChallenge': self.code_challenge,
                'codeChallengeMethod': 'S256',
                'redirectUri': self.config['redirect_uri'],
                'responseType': 'code',
                'action': 'login'
            }
            
            authorize_url = f"{self.config['base_urls']['auth']}/authorize"
            
            self.logger.debug(f"Authorize URL: {authorize_url}")
            self.logger.debug(f"Authorize params: {params}")
            
            # Gửi request authorize
            response = self.session.get(authorize_url, params=params, allow_redirects=False)
            
            self.logger.info(f"📡 Authorize request - Status: {response.status_code}")
            
            if response.status_code == 307:
                # Lấy redirect location để tìm session ID
                location = response.headers.get('Location', '')
                self.logger.debug(f"Redirect location: {location}")
                
                # Extract session ID từ redirect URL
                session_match = re.search(r'session=([^&]+)', location)
                if session_match:
                    self.session_id = session_match.group(1)
                    self.logger.info(f"✅ Đã lấy session ID: {self.session_id}")
                    return True
                else:
                    self.logger.error("❌ Không tìm thấy session ID trong redirect URL")
                    return False
            else:
                self.logger.error(f"❌ Authorize request thất bại - Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Lỗi trong OAuth flow: {e}")
            return False
    
    def login_with_credentials(self, username: str, password: str) -> Optional[str]:
        """
        Bước 2: Đăng nhập với username/password
        
        Args:
            username: Email đăng nhập
            password: Mật khẩu
            
        Returns:
            str: Authorization code nếu thành công, None nếu thất bại
        """
        try:
            self.logger.info(f"🔑 Đăng nhập với username: {username}")
            
            if not self.session_id:
                self.logger.error("❌ Chưa có session ID, cần chạy OAuth flow trước")
                return None
            
            # Chuẩn bị login request
            login_url = f"{self.config['base_urls']['accounts']}/api/v1/login"
            login_data = {
                "username": username,
                "password": password,
                "session": self.session_id
            }
            
            # Headers cho login request
            headers = {
                'Content-Type': 'application/json',
                'Referer': f"{self.config['base_urls']['auth']}/"
            }
            
            self.logger.debug(f"Login URL: {login_url}")
            self.logger.debug(f"Login data: {json.dumps({**login_data, 'password': '[HIDDEN]'})}")
            
            # Gửi login request
            response = self.session.post(
                login_url,
                json=login_data,
                headers=headers
            )
            
            self.logger.info(f"📡 Login request - Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    self.logger.debug(f"Login response: {response_data}")
                    
                    # Kiểm tra response có chứa thông tin thành công không
                    if 'success' in response_data and response_data['success']:
                        self.logger.info("✅ Đăng nhập thành công!")
                        return True
                    elif 'error' in response_data:
                        self.logger.error(f"❌ Đăng nhập thất bại: {response_data['error']}")
                        return None
                    else:
                        self.logger.info("✅ Đăng nhập có vẻ thành công (không có error)")
                        return True
                        
                except json.JSONDecodeError:
                    self.logger.warning("⚠️ Response không phải JSON, nhưng status 200")
                    return True
                    
            else:
                self.logger.error(f"❌ Login request thất bại - Status: {response.status_code}")
                try:
                    error_data = response.json()
                    self.logger.error(f"Error details: {error_data}")
                except:
                    self.logger.error(f"Response text: {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Lỗi trong quá trình đăng nhập: {e}")
            return None
    
    def get_access_token(self) -> Optional[str]:
        """
        Bước 3: Lấy access token sau khi đăng nhập thành công
        
        Returns:
            str: Access token nếu thành công, None nếu thất bại
        """
        try:
            self.logger.info("🎯 Đang lấy access token...")
            
            # Gửi refresh token request
            refresh_url = f"{self.config['base_urls']['accounts']}/api/v1/auth/refresh-token"
            refresh_data = {
                "clientId": self.config['client_id']
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Referer': self.config['base_urls']['studio'] + "/"
            }
            
            response = self.session.post(
                refresh_url,
                json=refresh_data,
                headers=headers
            )
            
            self.logger.info(f"📡 Refresh token request - Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    token_data = response.json()
                    self.logger.debug(f"Token response: {token_data}")
                    
                    # Tìm access token trong response
                    if 'accessToken' in token_data:
                        access_token = token_data['accessToken']
                        self.logger.info("✅ Đã lấy được access token!")
                        return access_token
                    elif 'access_token' in token_data:
                        access_token = token_data['access_token']
                        self.logger.info("✅ Đã lấy được access token!")
                        return access_token
                    else:
                        self.logger.error("❌ Không tìm thấy access token trong response")
                        return None
                        
                except json.JSONDecodeError:
                    self.logger.error("❌ Response không phải JSON")
                    return None
            else:
                self.logger.error(f"❌ Refresh token request thất bại - Status: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Lỗi khi lấy access token: {e}")
            return None

    def login_single_account(self, username: str, password: str) -> Tuple[str, Optional[str]]:
        """
        Đăng nhập một tài khoản duy nhất

        Args:
            username: Email đăng nhập
            password: Mật khẩu

        Returns:
            Tuple[str, Optional[str]]: (username, access_token hoặc error_message)
        """
        try:
            self.logger.info(f"🎯 Bắt đầu đăng nhập cho tài khoản: {username}")

            # Bước 1: Khởi tạo OAuth flow
            if not self.start_oauth_flow():
                return username, "ERROR: Không thể khởi tạo OAuth flow"

            # Delay giữa các requests
            time.sleep(self.config['delay_between_requests'])

            # Bước 2: Đăng nhập với credentials
            login_result = self.login_with_credentials(username, password)
            if not login_result:
                return username, "ERROR: Đăng nhập thất bại"

            # Delay giữa các requests
            time.sleep(self.config['delay_between_requests'])

            # Bước 3: Lấy access token
            access_token = self.get_access_token()
            if not access_token:
                return username, "ERROR: Không thể lấy access token"

            self.logger.info(f"✅ Đăng nhập thành công cho {username}")
            return username, access_token

        except Exception as e:
            error_msg = f"ERROR: {str(e)}"
            self.logger.error(f"❌ Lỗi khi đăng nhập {username}: {e}")
            return username, error_msg

    def login_batch_accounts(self, accounts_file: str, output_file: str = None) -> List[Tuple[str, str]]:
        """
        Đăng nhập batch nhiều tài khoản

        Args:
            accounts_file: File chứa danh sách tài khoản (format: email|password)
            output_file: File output để lưu kết quả

        Returns:
            List[Tuple[str, str]]: Danh sách (username, token_or_error)
        """
        results = []

        try:
            # Đọc danh sách tài khoản
            self.logger.info(f"📖 Đọc danh sách tài khoản từ: {accounts_file}")

            with open(accounts_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            accounts = []
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                if '|' not in line:
                    self.logger.warning(f"⚠️ Dòng {i} không đúng format (thiếu |): {line}")
                    continue

                parts = line.split('|', 1)
                if len(parts) != 2:
                    self.logger.warning(f"⚠️ Dòng {i} không đúng format: {line}")
                    continue

                username, password = parts[0].strip(), parts[1].strip()
                if username and password:
                    accounts.append((username, password))
                else:
                    self.logger.warning(f"⚠️ Dòng {i} có username hoặc password trống: {line}")

            self.logger.info(f"📊 Tìm thấy {len(accounts)} tài khoản hợp lệ")

            # Xử lý từng tài khoản
            for i, (username, password) in enumerate(accounts, 1):
                self.logger.info(f"🔄 Xử lý tài khoản {i}/{len(accounts)}: {username}")

                # Retry logic
                success = False
                for attempt in range(1, self.config['retry_count'] + 1):
                    try:
                        if attempt > 1:
                            self.logger.info(f"🔄 Thử lại lần {attempt} cho {username}")

                        result = self.login_single_account(username, password)
                        results.append(result)

                        if not result[1].startswith('ERROR:'):
                            success = True
                            break
                        else:
                            self.logger.warning(f"⚠️ Lần thử {attempt} thất bại: {result[1]}")

                    except Exception as e:
                        self.logger.error(f"❌ Lỗi lần thử {attempt}: {e}")
                        if attempt == self.config['retry_count']:
                            results.append((username, f"ERROR: {str(e)}"))

                # Delay giữa các tài khoản
                if i < len(accounts):
                    self.logger.info(f"⏳ Chờ {self.config['delay_between_accounts']} giây trước khi xử lý tài khoản tiếp theo...")
                    time.sleep(self.config['delay_between_accounts'])

            # Lưu kết quả
            if output_file:
                self.save_results(results, output_file)

            return results

        except Exception as e:
            self.logger.error(f"❌ Lỗi trong quá trình batch login: {e}")
            return results

    def save_results(self, results: List[Tuple[str, str]], output_file: str):
        """
        Lưu kết quả vào file

        Args:
            results: Danh sách kết quả
            output_file: Đường dẫn file output
        """
        try:
            self.logger.info(f"💾 Lưu kết quả vào: {output_file}")

            with open(output_file, 'w', encoding='utf-8') as f:
                # Header
                f.write("# Vbee Auto Login Results\n")
                f.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("# Format: email|jwt_token_or_error\n\n")

                # Results
                success_count = 0
                for username, token_or_error in results:
                    f.write(f"{username}|{token_or_error}\n")
                    if not token_or_error.startswith('ERROR:'):
                        success_count += 1

                # Summary
                f.write(f"\n# Summary: {success_count}/{len(results)} successful logins\n")

            self.logger.info(f"✅ Đã lưu {len(results)} kết quả vào {output_file}")
            self.logger.info(f"📊 Thống kê: {sum(1 for _, result in results if not result.startswith('ERROR:'))}/{len(results)} thành công")

        except Exception as e:
            self.logger.error(f"❌ Lỗi khi lưu kết quả: {e}")

    def print_summary(self, results: List[Tuple[str, str]]):
        """
        In tổng kết kết quả
        """
        success_results = [(u, t) for u, t in results if not t.startswith('ERROR:')]
        error_results = [(u, t) for u, t in results if t.startswith('ERROR:')]

        print("\n" + "="*60)
        print("📊 TỔNG KẾT KẾT QUẢ")
        print("="*60)
        print(f"✅ Thành công: {len(success_results)}/{len(results)} tài khoản")
        print(f"❌ Thất bại: {len(error_results)}/{len(results)} tài khoản")

        if success_results:
            print(f"\n🎯 CÁC TÀI KHOẢN THÀNH CÔNG:")
            for username, token in success_results:
                print(f"   ✅ {username} - Token: {token[:50]}...")

        if error_results:
            print(f"\n❌ CÁC TÀI KHOẢN THẤT BẠI:")
            for username, error in error_results:
                print(f"   ❌ {username} - {error}")


def main():
    """
    Hàm main để chạy tool
    """
    import argparse

    parser = argparse.ArgumentParser(description='Vbee Auto Login Tool')
    parser.add_argument('--accounts', '-a', required=True, help='File chứa danh sách tài khoản (email|password)')
    parser.add_argument('--output', '-o', help='File output để lưu kết quả')
    parser.add_argument('--config', '-c', default='config.json', help='File config')

    args = parser.parse_args()

    # Tạo output file name nếu không được chỉ định
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"vbee_tokens_{timestamp}.txt"

    print("🚀 VBEE AUTO LOGIN TOOL")
    print("="*50)
    print(f"📖 Accounts file: {args.accounts}")
    print(f"💾 Output file: {args.output}")
    print(f"⚙️ Config file: {args.config}")
    print("="*50)

    # Khởi tạo tool
    login_tool = VbeeAutoLogin(args.config)

    # Chạy batch login
    results = login_tool.login_batch_accounts(args.accounts, args.output)

    # In tổng kết
    login_tool.print_summary(results)

    print(f"\n💾 Kết quả đã được lưu vào: {args.output}")
    print("✅ Hoàn tất!")


if __name__ == "__main__":
    main()
