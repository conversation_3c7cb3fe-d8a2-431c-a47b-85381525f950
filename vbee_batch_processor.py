#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vbee Batch Processor
Multi-threaded batch processing with intelligent rate limiting
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Tuple, Dict, Optional, Callable
from dataclasses import dataclass
from queue import Queue
import logging

from vbee_auth_client import VbeeAuthClient, AuthResult, AuthStatus, IHttpClient, ILogger


@dataclass
class Account:
    """Account data class"""
    username: str
    password: str


@dataclass
class BatchConfig:
    """Batch processing configuration"""
    max_workers: int = 5
    delay_between_accounts: float = 1.0
    delay_between_requests: float = 2.0
    retry_count: int = 3
    rate_limit_per_second: float = 2.0
    timeout: int = 30


class RateLimiter:
    """Thread-safe rate limiter"""
    
    def __init__(self, max_calls_per_second: float):
        self.max_calls_per_second = max_calls_per_second
        self.min_interval = 1.0 / max_calls_per_second if max_calls_per_second > 0 else 0
        self.last_called = 0.0
        self.lock = threading.Lock()
    
    def acquire(self):
        """Acquire rate limit token"""
        with self.lock:
            now = time.time()
            time_passed = now - self.last_called
            
            if time_passed < self.min_interval:
                sleep_time = self.min_interval - time_passed
                time.sleep(sleep_time)
            
            self.last_called = time.time()


class ThreadSafeLogger(ILogger):
    """Thread-safe logger implementation"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.lock = threading.Lock()
    
    def _log(self, level: str, message: str):
        with self.lock:
            thread_id = threading.current_thread().ident
            formatted_message = f"[Thread-{thread_id}] {message}"
            getattr(self.logger, level)(formatted_message)
    
    def info(self, message: str) -> None:
        self._log('info', message)
    
    def error(self, message: str) -> None:
        self._log('error', message)
    
    def warning(self, message: str) -> None:
        self._log('warning', message)
    
    def debug(self, message: str) -> None:
        self._log('debug', message)


class HttpClientPool:
    """HTTP Client pool for multi-threading"""
    
    def __init__(self, pool_size: int, timeout: int = 30, user_agent: str = None):
        self.pool_size = pool_size
        self.timeout = timeout
        self.user_agent = user_agent
        self.pool = Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        
        # Initialize pool
        for _ in range(pool_size):
            from vbee_auth_client import HttpClient
            client = HttpClient(timeout=timeout, user_agent=user_agent)
            self.pool.put(client)
    
    def get_client(self) -> IHttpClient:
        """Get HTTP client from pool"""
        return self.pool.get()
    
    def return_client(self, client: IHttpClient):
        """Return HTTP client to pool"""
        self.pool.put(client)
    
    def close_all(self):
        """Close all clients in pool"""
        while not self.pool.empty():
            client = self.pool.get()
            if hasattr(client, 'close'):
                client.close()


class AccountProcessor:
    """Process single account with retry logic"""
    
    def __init__(self, config: Dict, batch_config: BatchConfig, 
                 http_client_pool: HttpClientPool, logger: ThreadSafeLogger,
                 rate_limiter: RateLimiter):
        self.config = config
        self.batch_config = batch_config
        self.http_client_pool = http_client_pool
        self.logger = logger
        self.rate_limiter = rate_limiter
    
    def process_account(self, account: Account) -> AuthResult:
        """
        Process single account with retry logic
        
        Args:
            account: Account to process
            
        Returns:
            AuthResult: Processing result
        """
        http_client = None
        
        try:
            # Get HTTP client from pool
            http_client = self.http_client_pool.get_client()
            
            # Create auth client
            auth_client = VbeeAuthClient(http_client, self.logger, self.config)
            
            # Retry logic
            last_result = None
            for attempt in range(1, self.batch_config.retry_count + 1):
                try:
                    if attempt > 1:
                        self.logger.info(f"🔄 Retry attempt {attempt} for {account.username}")
                        # Exponential backoff
                        delay = min(self.batch_config.delay_between_accounts * (2 ** (attempt - 1)), 30)
                        time.sleep(delay)
                    
                    # Apply rate limiting
                    self.rate_limiter.acquire()
                    
                    # Authenticate
                    result = auth_client.authenticate(account.username, account.password)
                    last_result = result
                    
                    if result.status == AuthStatus.SUCCESS:
                        self.logger.info(f"✅ Success for {account.username}")
                        return result
                    elif result.status == AuthStatus.RATE_LIMITED:
                        self.logger.warning(f"⚠️ Rate limited for {account.username}, waiting...")
                        time.sleep(self.batch_config.delay_between_accounts * 2)
                        continue
                    else:
                        self.logger.warning(f"⚠️ Attempt {attempt} failed for {account.username}: {result.error_message}")
                
                except Exception as e:
                    self.logger.error(f"❌ Exception in attempt {attempt} for {account.username}: {e}")
                    last_result = AuthResult(
                        status=AuthStatus.FAILED,
                        username=account.username,
                        error_message=str(e),
                        retry_count=attempt
                    )
            
            # All retries failed
            if last_result:
                last_result.retry_count = self.batch_config.retry_count
                return last_result
            else:
                return AuthResult(
                    status=AuthStatus.FAILED,
                    username=account.username,
                    error_message="All retry attempts failed",
                    retry_count=self.batch_config.retry_count
                )
        
        finally:
            # Return HTTP client to pool
            if http_client:
                self.http_client_pool.return_client(http_client)


class VbeeBatchProcessor:
    """
    Multi-threaded batch processor for Vbee authentication
    Handles multiple accounts concurrently with intelligent rate limiting
    """
    
    def __init__(self, config: Dict, batch_config: BatchConfig = None):
        self.config = config
        self.batch_config = batch_config or BatchConfig()
        
        # Initialize components
        self.rate_limiter = RateLimiter(self.batch_config.rate_limit_per_second)
        self.http_client_pool = HttpClientPool(
            pool_size=self.batch_config.max_workers,
            timeout=self.batch_config.timeout,
            user_agent=config.get('user_agent')
        )
        
        # Setup logging
        import logging
        base_logger = logging.getLogger('VbeeBatchProcessor')
        self.logger = ThreadSafeLogger(base_logger)
    
    def process_accounts(self, accounts: List[Account], 
                        progress_callback: Optional[Callable[[int, int], None]] = None) -> List[AuthResult]:
        """
        Process multiple accounts concurrently
        
        Args:
            accounts: List of accounts to process
            progress_callback: Optional callback for progress updates
            
        Returns:
            List[AuthResult]: Processing results
        """
        if not accounts:
            return []
        
        self.logger.info(f"🚀 Starting batch processing for {len(accounts)} accounts")
        self.logger.info(f"⚙️ Config: {self.batch_config.max_workers} workers, "
                        f"{self.batch_config.rate_limit_per_second} req/sec")
        
        results = []
        completed_count = 0
        
        # Create account processor
        processor = AccountProcessor(
            self.config, self.batch_config, self.http_client_pool, 
            self.logger, self.rate_limiter
        )
        
        # Process accounts with ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=self.batch_config.max_workers) as executor:
            # Submit all tasks
            future_to_account = {
                executor.submit(processor.process_account, account): account 
                for account in accounts
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_account):
                account = future_to_account[future]
                
                try:
                    result = future.result()
                    results.append(result)
                    completed_count += 1
                    
                    # Progress callback
                    if progress_callback:
                        progress_callback(completed_count, len(accounts))
                    
                    # Log progress
                    self.logger.info(f"📊 Progress: {completed_count}/{len(accounts)} completed")
                    
                except Exception as e:
                    self.logger.error(f"❌ Unexpected error processing {account.username}: {e}")
                    results.append(AuthResult(
                        status=AuthStatus.FAILED,
                        username=account.username,
                        error_message=f"Unexpected error: {str(e)}"
                    ))
                    completed_count += 1
        
        self.logger.info(f"✅ Batch processing completed: {completed_count}/{len(accounts)} processed")
        return results
    
    def close(self):
        """Clean up resources"""
        self.http_client_pool.close_all()


class AccountFileReader:
    """Utility class for reading account files"""
    
    @staticmethod
    def read_accounts(file_path: str) -> List[Account]:
        """
        Read accounts from file
        
        Args:
            file_path: Path to accounts file
            
        Returns:
            List[Account]: List of accounts
        """
        accounts = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                if '|' not in line:
                    print(f"⚠️ Line {i} invalid format (missing |): {line}")
                    continue
                
                parts = line.split('|', 1)
                if len(parts) != 2:
                    print(f"⚠️ Line {i} invalid format: {line}")
                    continue
                
                username, password = parts[0].strip(), parts[1].strip()
                if username and password:
                    accounts.append(Account(username=username, password=password))
                else:
                    print(f"⚠️ Line {i} has empty username or password: {line}")
            
            print(f"📊 Found {len(accounts)} valid accounts")
            return accounts
            
        except FileNotFoundError:
            print(f"❌ File not found: {file_path}")
            return []
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return []
