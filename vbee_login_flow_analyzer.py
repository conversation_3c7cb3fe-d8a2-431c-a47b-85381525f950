#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vbee Login Flow Deep Analyzer
Phân tích chi tiết flow đăng nhập để tạo tool tự động
"""

import json
import re
from urllib.parse import urlparse, parse_qs
from datetime import datetime

class VbeeLoginFlowAnalyzer:
    def __init__(self, har_file_path):
        self.har_file_path = har_file_path
        self.har_data = None
        self.entries = []
        self.login_sequence = []
        
    def load_har_file(self):
        """Đọc file HAR"""
        try:
            with open(self.har_file_path, 'r', encoding='utf-8') as f:
                self.har_data = json.load(f)
            self.entries = self.har_data['log']['entries']
            print(f"✅ Đã tải {len(self.entries)} entries từ file HAR")
            return True
        except Exception as e:
            print(f"❌ Lỗi khi đọc file HAR: {e}")
            return False
    
    def find_login_sequence(self):
        """Tìm sequence đăng nhập chính xác"""
        print("\n🔍 PHÂN TÍCH SEQUENCE ĐĂNG NHẬP CHI TIẾT")
        print("="*70)
        
        # Tìm các request quan trọng
        important_requests = []
        
        for i, entry in enumerate(self.entries):
            url = entry['request']['url']
            method = entry['request']['method']
            status = entry['response']['status']
            
            # Lọc các request quan trọng cho login
            if any(domain in url for domain in ['auth.vbee.vn', 'accounts.vbee.vn']):
                if any(path in url for path in ['/authorize', '/login', '/api/v1/login', '/refresh-token']):
                    
                    request_info = {
                        'index': i,
                        'method': method,
                        'url': url,
                        'status': status,
                        'timestamp': entry['startedDateTime'],
                        'headers': {},
                        'post_data': None,
                        'response_headers': {},
                        'response_body': None
                    }
                    
                    # Lấy request headers
                    for header in entry['request']['headers']:
                        request_info['headers'][header['name'].lower()] = header['value']
                    
                    # Lấy response headers
                    for header in entry['response']['headers']:
                        request_info['response_headers'][header['name'].lower()] = header['value']
                    
                    # Lấy POST data
                    if 'postData' in entry['request']:
                        request_info['post_data'] = entry['request']['postData']
                    
                    # Lấy response body
                    if 'content' in entry['response'] and 'text' in entry['response']['content']:
                        request_info['response_body'] = entry['response']['content']['text']
                    
                    important_requests.append(request_info)
        
        # Sắp xếp theo thời gian
        important_requests.sort(key=lambda x: x['timestamp'])
        
        # In ra sequence
        for i, req in enumerate(important_requests):
            print(f"\n📍 Bước {i+1}: {req['method']} {req['status']}")
            print(f"   URL: {req['url']}")
            print(f"   Time: {req['timestamp']}")
            
            # Headers quan trọng
            important_headers = ['authorization', 'cookie', 'content-type', 'referer', 'user-agent']
            for header in important_headers:
                if header in req['headers']:
                    value = req['headers'][header]
                    if header == 'cookie' and len(value) > 100:
                        print(f"   🍪 {header}: {value[:100]}...")
                    elif header == 'authorization':
                        print(f"   🔑 {header}: {value}")
                    else:
                        print(f"   📋 {header}: {value}")
            
            # POST data
            if req['post_data']:
                print(f"   📤 POST Data:")
                if 'text' in req['post_data']:
                    data = req['post_data']['text']
                    try:
                        json_data = json.loads(data)
                        print(f"      JSON: {json.dumps(json_data, indent=6, ensure_ascii=False)}")
                    except:
                        print(f"      Raw: {data}")
            
            # Response headers quan trọng
            response_headers = ['set-cookie', 'location', 'authorization']
            for header in response_headers:
                if header in req['response_headers']:
                    value = req['response_headers'][header]
                    if header == 'set-cookie':
                        print(f"   🍪 Response Set-Cookie: {value}")
                    elif header == 'location':
                        print(f"   📍 Redirect Location: {value}")
                    else:
                        print(f"   📋 Response {header}: {value}")
            
            # Response body (nếu có JWT token)
            if req['response_body']:
                try:
                    response_json = json.loads(req['response_body'])
                    if 'accessToken' in response_json or 'token' in response_json:
                        print(f"   🎯 Response Body (có token):")
                        for key, value in response_json.items():
                            if 'token' in key.lower():
                                print(f"      {key}: {str(value)[:50]}...")
                            else:
                                print(f"      {key}: {value}")
                except:
                    if len(req['response_body']) < 200:
                        print(f"   📄 Response Body: {req['response_body']}")
        
        self.login_sequence = important_requests
        return important_requests
    
    def extract_oauth_flow(self):
        """Phân tích OAuth flow"""
        print("\n🔐 PHÂN TÍCH OAUTH FLOW")
        print("="*50)
        
        oauth_params = {}
        
        for req in self.login_sequence:
            url = req['url']
            
            # Tìm authorize request
            if '/authorize' in url:
                parsed_url = urlparse(url)
                params = parse_qs(parsed_url.query)
                
                print(f"🎯 OAuth Authorize Request:")
                print(f"   URL: {url}")
                for key, value in params.items():
                    oauth_params[key] = value[0] if value else None
                    print(f"   {key}: {value[0] if value else 'None'}")
            
            # Tìm login request
            elif '/login' in url and req['method'] == 'POST':
                print(f"\n🔑 Login Request:")
                print(f"   URL: {url}")
                if req['post_data'] and 'text' in req['post_data']:
                    try:
                        login_data = json.loads(req['post_data']['text'])
                        print(f"   Login Data Structure:")
                        for key in login_data.keys():
                            if 'password' in key.lower():
                                print(f"     {key}: [REQUIRED - PASSWORD FIELD]")
                            else:
                                print(f"     {key}: [REQUIRED - {key.upper()} FIELD]")
                    except:
                        pass
        
        return oauth_params
    
    def generate_implementation_guide(self):
        """Tạo hướng dẫn implement"""
        print("\n📋 HƯỚNG DẪN IMPLEMENT TOOL TỰ ĐỘNG ĐĂNG NHẬP")
        print("="*60)
        
        print("""
🎯 FLOW ĐĂNG NHẬP VBEE:

1. BƯỚC 1: Khởi tạo OAuth Flow
   - URL: https://auth.vbee.vn/authorize
   - Params: clientId, codeChallenge, codeChallengeMethod, redirectUri, responseType, action
   - Method: GET
   - Lấy session ID từ response

2. BƯỚC 2: Gửi thông tin đăng nhập
   - URL: https://accounts.vbee.vn/api/v1/login
   - Method: POST
   - Headers: Content-Type: application/json
   - Body: {"username": "email", "password": "password", "session": "session_id"}

3. BƯỚC 3: Xử lý redirect và lấy authorization code
   - Theo dõi redirect responses
   - Lấy code từ redirect URL

4. BƯỚC 4: Exchange code để lấy access token
   - URL: https://accounts.vbee.vn/api/v1/auth/refresh-token
   - Method: POST
   - Body: {"clientId": "aivoice-web-application"}

🔧 CÁC THÀNH PHẦN CẦN IMPLEMENT:
- OAuth PKCE flow (code challenge/verifier)
- Session management
- Cookie handling
- Error handling và retry logic
- Rate limiting
        """)
    
    def run_analysis(self):
        """Chạy toàn bộ phân tích"""
        print("🚀 PHÂN TÍCH DEEP VBEE LOGIN FLOW")
        print("="*50)
        
        if not self.load_har_file():
            return False
        
        # Tìm login sequence
        self.find_login_sequence()
        
        # Phân tích OAuth flow
        self.extract_oauth_flow()
        
        # Tạo hướng dẫn implement
        self.generate_implementation_guide()
        
        return True

def main():
    analyzer = VbeeLoginFlowAnalyzer("studio.vbee.vn.har")
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
