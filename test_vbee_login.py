#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho Vbee Auto Login Tool
"""

import sys
import os
from vbee_auto_login import VbeeAutoLogin

def test_oauth_flow():
    """
    Test OAuth flow initialization
    """
    print("🧪 Testing OAuth Flow...")
    
    login_tool = VbeeAutoLogin("config.json")
    
    # Test PKCE generation
    login_tool.generate_pkce_params()
    
    if login_tool.code_verifier and login_tool.code_challenge:
        print("✅ PKCE parameters generated successfully")
        print(f"   Code Verifier: {login_tool.code_verifier[:20]}...")
        print(f"   Code Challenge: {login_tool.code_challenge}")
    else:
        print("❌ Failed to generate PKCE parameters")
        return False
    
    # Test OAuth flow
    result = login_tool.start_oauth_flow()
    
    if result and login_tool.session_id:
        print("✅ OAuth flow initialized successfully")
        print(f"   Session ID: {login_tool.session_id}")
        return True
    else:
        print("❌ Failed to initialize OAuth flow")
        return False

def test_config_loading():
    """
    Test config loading
    """
    print("🧪 Testing Config Loading...")
    
    try:
        login_tool = VbeeAutoLogin("config.json")
        
        required_keys = ['timeout', 'retry_count', 'client_id', 'base_urls']
        for key in required_keys:
            if key not in login_tool.config:
                print(f"❌ Missing config key: {key}")
                return False
        
        print("✅ Config loaded successfully")
        print(f"   Timeout: {login_tool.config['timeout']}")
        print(f"   Retry Count: {login_tool.config['retry_count']}")
        print(f"   Client ID: {login_tool.config['client_id']}")
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def test_accounts_file_parsing():
    """
    Test parsing accounts file
    """
    print("🧪 Testing Accounts File Parsing...")
    
    # Tạo file test
    test_accounts = """# Test accounts file
<EMAIL>|password1
<EMAIL>|password2
# This is a comment
invalid_line_without_pipe
<EMAIL>|password3
"""
    
    test_file = "test_accounts.txt"
    
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_accounts)
        
        # Parse file
        with open(test_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        accounts = []
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            if '|' not in line:
                print(f"⚠️ Invalid line {i}: {line}")
                continue
            
            parts = line.split('|', 1)
            if len(parts) == 2:
                username, password = parts[0].strip(), parts[1].strip()
                if username and password:
                    accounts.append((username, password))
        
        print(f"✅ Parsed {len(accounts)} valid accounts")
        for username, _ in accounts:
            print(f"   - {username}")
        
        # Cleanup
        os.remove(test_file)
        return True
        
    except Exception as e:
        print(f"❌ Accounts file parsing failed: {e}")
        return False

def test_session_setup():
    """
    Test HTTP session setup
    """
    print("🧪 Testing Session Setup...")
    
    try:
        login_tool = VbeeAutoLogin("config.json")
        
        # Check session headers
        required_headers = ['User-Agent', 'Accept', 'Accept-Language']
        for header in required_headers:
            if header not in login_tool.session.headers:
                print(f"❌ Missing header: {header}")
                return False
        
        print("✅ Session setup successfully")
        print(f"   User-Agent: {login_tool.session.headers['User-Agent'][:50]}...")
        print(f"   Timeout: {login_tool.session.timeout}")
        return True
        
    except Exception as e:
        print(f"❌ Session setup failed: {e}")
        return False

def run_all_tests():
    """
    Chạy tất cả tests
    """
    print("🚀 VBEE AUTO LOGIN TOOL - UNIT TESTS")
    print("="*50)
    
    tests = [
        ("Config Loading", test_config_loading),
        ("Session Setup", test_session_setup),
        ("Accounts File Parsing", test_accounts_file_parsing),
        ("OAuth Flow", test_oauth_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
