# Vbee Auto Login Tool

Tool tự động đăng nhập vào hệ thống Vbee và lấy JWT Bearer Token cho nhiều tài khoản cùng lúc.

## 🚀 Tính năng

- ✅ Tự động đăng nhập vào hệ thống Vbee
- ✅ Hỗ trợ đăng nhập batch cho nhiều tài khoản
- ✅ Lấy JWT Bearer Token tự động
- ✅ Xử lý lỗi robust với retry mechanism
- ✅ Rate limiting để tránh bị block
- ✅ Logging chi tiết cho debug
- ✅ Cấu hình linh hoạt qua file config
- ✅ Hỗ trợ OAuth PKCE flow

## 📋 Yêu cầu

- Python 3.7+
- Thư viện `requests`

## 🛠️ Cài đặt

1. Clone hoặc download source code
2. Cài đặt dependencies:

```bash
pip install requests
```

## 📁 Cấu trúc file

```
vbee_auto_login/
├── vbee_auto_login.py      # Tool chính
├── config.json             # File cấu hình
├── accounts_example.txt    # File mẫu danh sách tài khoản
├── README.md              # Hướng dẫn này
├── har_analyzer.py        # Tool phân tích HAR (optional)
└── vbee_login_flow_analyzer.py  # Tool phân tích flow (optional)
```

## ⚙️ Cấu hình

### File config.json

```json
{
  "timeout": 30,                    // Timeout cho HTTP requests (giây)
  "retry_count": 3,                 // Số lần retry khi thất bại
  "delay_between_requests": 2,      // Delay giữa các requests (giây)
  "delay_between_accounts": 5,      // Delay giữa các tài khoản (giây)
  "user_agent": "Mozilla/5.0...",   // User-Agent string
  "client_id": "aivoice-web-application",
  "redirect_uri": "https://studio.vbee.vn/studio/text-to-speech?",
  "base_urls": {
    "auth": "https://auth.vbee.vn",
    "accounts": "https://accounts.vbee.vn",
    "studio": "https://studio.vbee.vn"
  }
}
```

### File danh sách tài khoản

Tạo file text với format: `email|password` (mỗi dòng một tài khoản)

```
# Ví dụ file accounts.txt
<EMAIL>|password123
<EMAIL>|mypassword
<EMAIL>|secretpass
```

## 🚀 Sử dụng

### Cách 1: Command line

```bash
python vbee_auto_login.py --accounts accounts.txt --output tokens.txt
```

### Cách 2: Sử dụng trong code

```python
from vbee_auto_login import VbeeAutoLogin

# Khởi tạo tool
login_tool = VbeeAutoLogin("config.json")

# Đăng nhập một tài khoản
username, token = login_tool.login_single_account("<EMAIL>", "password")

# Đăng nhập batch
results = login_tool.login_batch_accounts("accounts.txt", "output.txt")
```

## 📊 Output

Tool sẽ tạo file output với format:

```
# Vbee Auto Login Results
# Generated: 2025-07-29 19:47:14
# Format: email|jwt_token_or_error

<EMAIL>|eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
<EMAIL>|ERROR: Đăng nhập thất bại
<EMAIL>|eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Summary: 2/3 successful logins
```

## 📝 Logging

Tool tự động tạo log file với tên `vbee_login_YYYYMMDD_HHMMSS.log` chứa:

- Thông tin chi tiết về từng request
- Lỗi và debug information
- Timestamp cho mỗi action
- Status codes và response data

## 🔧 Tùy chỉnh

### Thay đổi delay

Để tránh bị rate limit, bạn có thể tăng delay:

```json
{
  "delay_between_requests": 5,
  "delay_between_accounts": 10
}
```

### Thay đổi retry logic

```json
{
  "retry_count": 5,
  "timeout": 60
}
```

## 🐛 Troubleshooting

### Lỗi thường gặp

1. **"Không thể khởi tạo OAuth flow"**
   - Kiểm tra kết nối internet
   - Kiểm tra URL trong config

2. **"Đăng nhập thất bại"**
   - Kiểm tra username/password
   - Kiểm tra tài khoản có bị khóa không

3. **"Không thể lấy access token"**
   - Có thể do session timeout
   - Thử tăng delay giữa các requests

### Debug

Để debug chi tiết, xem file log được tạo tự động.

## 🔒 Bảo mật

- ⚠️ Không commit file chứa password lên git
- ⚠️ Sử dụng file .gitignore để loại trừ accounts.txt
- ⚠️ Bảo mật file log vì có thể chứa thông tin nhạy cảm

## 📄 License

MIT License

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Hãy tạo issue hoặc pull request.

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra file log
2. Xem phần Troubleshooting
3. Tạo issue với thông tin chi tiết

---

**Lưu ý**: Tool này chỉ dành cho mục đích học tập và testing. Hãy tuân thủ Terms of Service của Vbee khi sử dụng.
