#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script để tìm endpoint tr<PERSON> về token thực sự
"""

import json
import re
from urllib.parse import urlparse, parse_qs

def find_token_responses():
    """
    Tìm tất cả responses có chứa token
    """
    print("🔍 TÌM KIẾM TOKEN TRONG HAR FILE")
    print("="*50)
    
    with open("studio.vbee.vn.har", 'r', encoding='utf-8') as f:
        har_data = json.load(f)
    
    entries = har_data['log']['entries']
    
    token_responses = []
    
    for i, entry in enumerate(entries):
        url = entry['request']['url']
        method = entry['request']['method']
        status = entry['response']['status']
        
        # Kiểm tra response body có chứa token không
        if 'content' in entry['response'] and 'text' in entry['response']['content']:
            response_text = entry['response']['content']['text']
            
            # Tì<PERSON> các pattern token
            token_patterns = [
                r'"accessToken":\s*"([^"]+)"',
                r'"access_token":\s*"([^"]+)"',
                r'"token":\s*"([^"]+)"',
                r'"jwt":\s*"([^"]+)"',
                r'"bearer":\s*"([^"]+)"',
                r'eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*'  # JWT pattern
            ]
            
            for pattern in token_patterns:
                matches = re.findall(pattern, response_text, re.IGNORECASE)
                if matches:
                    token_responses.append({
                        'index': i,
                        'url': url,
                        'method': method,
                        'status': status,
                        'timestamp': entry['startedDateTime'],
                        'tokens_found': matches,
                        'response_text': response_text[:500] + "..." if len(response_text) > 500 else response_text
                    })
                    break
    
    print(f"🎯 Tìm thấy {len(token_responses)} responses có chứa token:")
    
    for i, resp in enumerate(token_responses):
        print(f"\n📍 Response {i+1}:")
        print(f"   URL: {resp['url']}")
        print(f"   Method: {resp['method']} - Status: {resp['status']}")
        print(f"   Time: {resp['timestamp']}")
        print(f"   Tokens found: {len(resp['tokens_found'])}")
        for j, token in enumerate(resp['tokens_found']):
            if len(token) > 50:
                print(f"     Token {j+1}: {token[:50]}...")
            else:
                print(f"     Token {j+1}: {token}")
        print(f"   Response preview: {resp['response_text'][:200]}...")

def find_post_login_flow():
    """
    Tìm flow sau khi đăng nhập thành công
    """
    print("\n🔍 TÌM FLOW SAU KHI ĐĂNG NHẬP")
    print("="*50)
    
    with open("studio.vbee.vn.har", 'r', encoding='utf-8') as f:
        har_data = json.load(f)
    
    entries = har_data['log']['entries']
    
    # Tìm login request
    login_index = None
    for i, entry in enumerate(entries):
        url = entry['request']['url']
        method = entry['request']['method']
        
        if '/api/v1/login' in url and method == 'POST':
            login_index = i
            print(f"🔑 Tìm thấy login request tại index {i}")
            print(f"   URL: {url}")
            print(f"   Time: {entry['startedDateTime']}")
            break
    
    if login_index is None:
        print("❌ Không tìm thấy login request")
        return
    
    # Phân tích 20 requests sau login
    print(f"\n📋 20 REQUESTS SAU LOGIN:")
    for i in range(login_index + 1, min(login_index + 21, len(entries))):
        entry = entries[i]
        url = entry['request']['url']
        method = entry['request']['method']
        status = entry['response']['status']
        
        print(f"\n[{i:3d}] {method} {status} - {entry['startedDateTime']}")
        print(f"      {url}")
        
        # Kiểm tra POST data
        if method == 'POST' and 'postData' in entry['request']:
            post_data = entry['request']['postData']
            if 'text' in post_data:
                try:
                    json_data = json.loads(post_data['text'])
                    print(f"      POST: {json.dumps(json_data, ensure_ascii=False)}")
                except:
                    print(f"      POST: {post_data['text'][:100]}...")
        
        # Kiểm tra response có token không
        if 'content' in entry['response'] and 'text' in entry['response']['content']:
            response_text = entry['response']['content']['text']
            if any(keyword in response_text.lower() for keyword in ['token', 'jwt', 'bearer', 'access']):
                print(f"      🎯 Response có thể chứa token: {response_text[:200]}...")

def find_redirect_flow():
    """
    Tìm redirect flow sau login
    """
    print("\n🔍 TÌM REDIRECT FLOW")
    print("="*50)
    
    with open("studio.vbee.vn.har", 'r', encoding='utf-8') as f:
        har_data = json.load(f)
    
    entries = har_data['log']['entries']
    
    redirects = []
    
    for i, entry in enumerate(entries):
        status = entry['response']['status']
        
        if status in [301, 302, 307, 308]:  # Redirect status codes
            url = entry['request']['url']
            location = None
            
            # Tìm Location header
            for header in entry['response']['headers']:
                if header['name'].lower() == 'location':
                    location = header['value']
                    break
            
            redirects.append({
                'index': i,
                'url': url,
                'status': status,
                'location': location,
                'timestamp': entry['startedDateTime']
            })
    
    print(f"🎯 Tìm thấy {len(redirects)} redirects:")
    
    for redirect in redirects:
        print(f"\n📍 Redirect {redirect['index']}:")
        print(f"   From: {redirect['url']}")
        print(f"   To: {redirect['location']}")
        print(f"   Status: {redirect['status']}")
        print(f"   Time: {redirect['timestamp']}")
        
        # Kiểm tra có code trong redirect không
        if redirect['location'] and 'code=' in redirect['location']:
            code_match = re.search(r'code=([^&]+)', redirect['location'])
            if code_match:
                print(f"   🎯 Authorization Code: {code_match.group(1)}")

def main():
    print("🚀 DEBUG VBEE TOKEN FLOW")
    print("="*50)
    
    find_token_responses()
    find_post_login_flow()
    find_redirect_flow()

if __name__ == "__main__":
    main()
