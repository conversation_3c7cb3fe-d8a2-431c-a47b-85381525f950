#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vbee Exception Classes
Custom exceptions for better error handling
"""

from typing import Optional, Dict, Any
from enum import Enum


class ErrorCode(Enum):
    """Error codes for categorizing failures"""
    
    # Authentication errors
    AUTH_INVALID_CREDENTIALS = "AUTH_001"
    AUTH_OAUTH_FLOW_FAILED = "AUTH_002"
    AUTH_SESSION_EXPIRED = "AUTH_003"
    AUTH_TOKEN_EXCHANGE_FAILED = "AUTH_004"
    AUTH_RATE_LIMITED = "AUTH_005"
    
    # Network errors
    NETWORK_CONNECTION_FAILED = "NET_001"
    NETWORK_TIMEOUT = "NET_002"
    NETWORK_DNS_FAILED = "NET_003"
    NETWORK_SSL_ERROR = "NET_004"
    
    # Configuration errors
    CONFIG_INVALID_FORMAT = "CFG_001"
    CONFIG_MISSING_REQUIRED = "CFG_002"
    CONFIG_INVALID_VALUE = "CFG_003"
    
    # File errors
    FILE_NOT_FOUND = "FILE_001"
    FILE_PERMISSION_DENIED = "FILE_002"
    FILE_INVALID_FORMAT = "FILE_003"
    
    # Processing errors
    PROC_THREAD_POOL_ERROR = "PROC_001"
    PROC_RESOURCE_EXHAUSTED = "PROC_002"
    PROC_UNEXPECTED_ERROR = "PROC_003"
    
    # API errors
    API_INVALID_RESPONSE = "API_001"
    API_SERVER_ERROR = "API_002"
    API_QUOTA_EXCEEDED = "API_003"


class VbeeException(Exception):
    """Base exception class for Vbee operations"""
    
    def __init__(self, message: str, error_code: ErrorCode = None, 
                 details: Optional[Dict[str, Any]] = None, 
                 original_exception: Optional[Exception] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.original_exception = original_exception
    
    def __str__(self) -> str:
        error_info = f"[{self.error_code.value}] " if self.error_code else ""
        return f"{error_info}{self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging/serialization"""
        return {
            "error_code": self.error_code.value if self.error_code else None,
            "message": self.message,
            "details": self.details,
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class AuthenticationError(VbeeException):
    """Authentication related errors"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.AUTH_INVALID_CREDENTIALS,
                 username: str = None, **kwargs):
        super().__init__(message, error_code, **kwargs)
        if username:
            self.details["username"] = username


class NetworkError(VbeeException):
    """Network related errors"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.NETWORK_CONNECTION_FAILED,
                 url: str = None, status_code: int = None, **kwargs):
        super().__init__(message, error_code, **kwargs)
        if url:
            self.details["url"] = url
        if status_code:
            self.details["status_code"] = status_code


class ConfigurationError(VbeeException):
    """Configuration related errors"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.CONFIG_INVALID_FORMAT,
                 config_key: str = None, **kwargs):
        super().__init__(message, error_code, **kwargs)
        if config_key:
            self.details["config_key"] = config_key


class FileError(VbeeException):
    """File operation related errors"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.FILE_NOT_FOUND,
                 file_path: str = None, **kwargs):
        super().__init__(message, error_code, **kwargs)
        if file_path:
            self.details["file_path"] = file_path


class ProcessingError(VbeeException):
    """Processing related errors"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.PROC_UNEXPECTED_ERROR,
                 thread_id: str = None, **kwargs):
        super().__init__(message, error_code, **kwargs)
        if thread_id:
            self.details["thread_id"] = thread_id


class APIError(VbeeException):
    """API related errors"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.API_INVALID_RESPONSE,
                 endpoint: str = None, response_data: Any = None, **kwargs):
        super().__init__(message, error_code, **kwargs)
        if endpoint:
            self.details["endpoint"] = endpoint
        if response_data:
            self.details["response_data"] = response_data


class RateLimitError(VbeeException):
    """Rate limiting related errors"""
    
    def __init__(self, message: str = "Rate limit exceeded", 
                 retry_after: int = None, **kwargs):
        super().__init__(message, ErrorCode.AUTH_RATE_LIMITED, **kwargs)
        if retry_after:
            self.details["retry_after"] = retry_after


class ExceptionHandler:
    """Centralized exception handling utility"""
    
    @staticmethod
    def handle_requests_exception(e: Exception, url: str = None) -> VbeeException:
        """Convert requests exceptions to VbeeException"""
        import requests
        
        if isinstance(e, requests.exceptions.Timeout):
            return NetworkError(
                f"Request timeout: {str(e)}", 
                ErrorCode.NETWORK_TIMEOUT,
                url=url,
                original_exception=e
            )
        elif isinstance(e, requests.exceptions.ConnectionError):
            return NetworkError(
                f"Connection failed: {str(e)}", 
                ErrorCode.NETWORK_CONNECTION_FAILED,
                url=url,
                original_exception=e
            )
        elif isinstance(e, requests.exceptions.SSLError):
            return NetworkError(
                f"SSL error: {str(e)}", 
                ErrorCode.NETWORK_SSL_ERROR,
                url=url,
                original_exception=e
            )
        elif isinstance(e, requests.exceptions.HTTPError):
            status_code = e.response.status_code if e.response else None
            return NetworkError(
                f"HTTP error: {str(e)}", 
                ErrorCode.NETWORK_CONNECTION_FAILED,
                url=url,
                status_code=status_code,
                original_exception=e
            )
        else:
            return NetworkError(
                f"Network error: {str(e)}", 
                ErrorCode.NETWORK_CONNECTION_FAILED,
                url=url,
                original_exception=e
            )
    
    @staticmethod
    def handle_file_exception(e: Exception, file_path: str = None) -> VbeeException:
        """Convert file exceptions to VbeeException"""
        if isinstance(e, FileNotFoundError):
            return FileError(
                f"File not found: {str(e)}", 
                ErrorCode.FILE_NOT_FOUND,
                file_path=file_path,
                original_exception=e
            )
        elif isinstance(e, PermissionError):
            return FileError(
                f"Permission denied: {str(e)}", 
                ErrorCode.FILE_PERMISSION_DENIED,
                file_path=file_path,
                original_exception=e
            )
        elif isinstance(e, (UnicodeDecodeError, UnicodeEncodeError)):
            return FileError(
                f"File encoding error: {str(e)}", 
                ErrorCode.FILE_INVALID_FORMAT,
                file_path=file_path,
                original_exception=e
            )
        else:
            return FileError(
                f"File operation error: {str(e)}", 
                ErrorCode.FILE_INVALID_FORMAT,
                file_path=file_path,
                original_exception=e
            )
    
    @staticmethod
    def handle_json_exception(e: Exception, data: str = None) -> VbeeException:
        """Convert JSON exceptions to VbeeException"""
        import json
        
        if isinstance(e, json.JSONDecodeError):
            return APIError(
                f"Invalid JSON response: {str(e)}", 
                ErrorCode.API_INVALID_RESPONSE,
                response_data=data[:200] + "..." if data and len(data) > 200 else data,
                original_exception=e
            )
        else:
            return APIError(
                f"JSON processing error: {str(e)}", 
                ErrorCode.API_INVALID_RESPONSE,
                original_exception=e
            )
    
    @staticmethod
    def categorize_http_status(status_code: int, url: str = None) -> Optional[VbeeException]:
        """Categorize HTTP status codes into appropriate exceptions"""
        if 200 <= status_code < 300:
            return None  # Success
        elif status_code == 401:
            return AuthenticationError(
                "Authentication failed - Invalid credentials",
                ErrorCode.AUTH_INVALID_CREDENTIALS,
                details={"status_code": status_code, "url": url}
            )
        elif status_code == 403:
            return AuthenticationError(
                "Access forbidden - Insufficient permissions",
                ErrorCode.AUTH_INVALID_CREDENTIALS,
                details={"status_code": status_code, "url": url}
            )
        elif status_code == 429:
            return RateLimitError(
                "Rate limit exceeded",
                details={"status_code": status_code, "url": url}
            )
        elif 400 <= status_code < 500:
            return APIError(
                f"Client error - HTTP {status_code}",
                ErrorCode.API_INVALID_RESPONSE,
                details={"status_code": status_code, "url": url}
            )
        elif 500 <= status_code < 600:
            return APIError(
                f"Server error - HTTP {status_code}",
                ErrorCode.API_SERVER_ERROR,
                details={"status_code": status_code, "url": url}
            )
        else:
            return NetworkError(
                f"Unexpected HTTP status - {status_code}",
                ErrorCode.NETWORK_CONNECTION_FAILED,
                details={"status_code": status_code, "url": url}
            )


class RetryableError(VbeeException):
    """Marks an error as retryable"""
    
    def __init__(self, message: str, retry_after: int = None, max_retries: int = 3, **kwargs):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after
        self.max_retries = max_retries


def is_retryable_error(exception: Exception) -> bool:
    """Check if an error is retryable"""
    if isinstance(exception, RetryableError):
        return True
    
    if isinstance(exception, VbeeException):
        retryable_codes = {
            ErrorCode.NETWORK_TIMEOUT,
            ErrorCode.NETWORK_CONNECTION_FAILED,
            ErrorCode.AUTH_RATE_LIMITED,
            ErrorCode.API_SERVER_ERROR,
            ErrorCode.PROC_RESOURCE_EXHAUSTED
        }
        return exception.error_code in retryable_codes
    
    # Check for common retryable exceptions
    import requests
    retryable_types = (
        requests.exceptions.Timeout,
        requests.exceptions.ConnectionError,
        ConnectionResetError,
        ConnectionAbortedError
    )
    
    return isinstance(exception, retryable_types)


def get_retry_delay(exception: Exception, attempt: int) -> int:
    """Get retry delay for an exception"""
    if isinstance(exception, (RateLimitError, RetryableError)):
        if hasattr(exception, 'retry_after') and exception.retry_after:
            return exception.retry_after
    
    # Exponential backoff: 2^attempt seconds, max 60 seconds
    return min(2 ** attempt, 60)
