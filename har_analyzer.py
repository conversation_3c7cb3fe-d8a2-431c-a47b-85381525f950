#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HAR File Analyzer for Vbee Login Flow
Phân tích file HAR để tìm ra các endpoint và flow đăng nhập của Vbee
"""

import json
import re
from urllib.parse import urlparse, parse_qs
from collections import defaultdict
import sys
from datetime import datetime

class VbeeHARAnalyzer:
    def __init__(self, har_file_path):
        """
        Khởi tạo analyzer với đường dẫn file HAR
        """
        self.har_file_path = har_file_path
        self.har_data = None
        self.entries = []
        self.auth_endpoints = []
        self.api_endpoints = []
        self.login_flow = []
        
    def load_har_file(self):
        """
        Đọc và parse file HAR
        """
        try:
            print(f"[INFO] Đang đọc file HAR: {self.har_file_path}")
            with open(self.har_file_path, 'r', encoding='utf-8') as f:
                self.har_data = json.load(f)
            
            self.entries = self.har_data['log']['entries']
            print(f"[INFO] Đã tải {len(self.entries)} entries từ file HAR")
            return True
            
        except Exception as e:
            print(f"[ERROR] Lỗi khi đọc file HAR: {e}")
            return False
    
    def analyze_domains(self):
        """
        Phân tích các domain được sử dụng
        """
        domains = defaultdict(int)
        
        for entry in self.entries:
            url = entry['request']['url']
            domain = urlparse(url).netloc
            domains[domain] += 1
        
        print("\n" + "="*60)
        print("PHÂN TÍCH DOMAINS")
        print("="*60)
        
        for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
            print(f"{domain:<40} {count:>6} requests")
        
        return domains
    
    def find_auth_endpoints(self):
        """
        Tìm các endpoint liên quan đến authentication
        """
        auth_keywords = [
            'auth', 'login', 'signin', 'authorize', 'token', 'oauth',
            'authentication', 'credential', 'session', 'jwt'
        ]
        
        print("\n" + "="*60)
        print("CÁC ENDPOINT AUTHENTICATION")
        print("="*60)
        
        for i, entry in enumerate(self.entries):
            url = entry['request']['url']
            method = entry['request']['method']
            
            # Kiểm tra URL có chứa từ khóa auth không
            url_lower = url.lower()
            if any(keyword in url_lower for keyword in auth_keywords):
                
                # Lấy thông tin request
                request_info = {
                    'index': i,
                    'method': method,
                    'url': url,
                    'status': entry['response']['status'],
                    'timestamp': entry['startedDateTime']
                }
                
                # Lấy headers
                headers = {}
                for header in entry['request']['headers']:
                    headers[header['name']] = header['value']
                
                # Lấy post data nếu có
                post_data = None
                if 'postData' in entry['request']:
                    post_data = entry['request']['postData']
                
                request_info['headers'] = headers
                request_info['post_data'] = post_data
                
                self.auth_endpoints.append(request_info)
                
                print(f"\n[{i:4d}] {method} {entry['response']['status']}")
                print(f"       {url}")
                print(f"       Time: {entry['startedDateTime']}")
        
        return self.auth_endpoints
    
    def analyze_login_flow(self):
        """
        Phân tích flow đăng nhập theo thứ tự thời gian
        """
        print("\n" + "="*60)
        print("PHÂN TÍCH LOGIN FLOW (THEO THỨ TỰ THỜI GIAN)")
        print("="*60)
        
        # Sắp xếp auth endpoints theo thời gian
        sorted_auth = sorted(self.auth_endpoints, key=lambda x: x['timestamp'])
        
        for i, endpoint in enumerate(sorted_auth):
            print(f"\nBước {i+1}: {endpoint['method']} - Status: {endpoint['status']}")
            print(f"URL: {endpoint['url']}")
            print(f"Time: {endpoint['timestamp']}")
            
            # Phân tích headers quan trọng
            important_headers = ['authorization', 'cookie', 'content-type', 'referer']
            for header_name in important_headers:
                for h_name, h_value in endpoint['headers'].items():
                    if h_name.lower() == header_name:
                        if header_name == 'authorization' and 'bearer' in h_value.lower():
                            print(f"  🔑 {h_name}: {h_value[:50]}...")
                        elif header_name == 'cookie' and len(h_value) > 100:
                            print(f"  🍪 {h_name}: {h_value[:100]}...")
                        else:
                            print(f"  📋 {h_name}: {h_value}")
            
            # Phân tích POST data
            if endpoint['post_data']:
                print(f"  📤 POST Data:")
                if 'text' in endpoint['post_data']:
                    post_text = endpoint['post_data']['text']
                    if len(post_text) > 200:
                        print(f"       {post_text[:200]}...")
                    else:
                        print(f"       {post_text}")
        
        return sorted_auth
    
    def extract_login_credentials_pattern(self):
        """
        Tìm pattern để gửi thông tin đăng nhập
        """
        print("\n" + "="*60)
        print("PATTERN ĐĂNG NHẬP")
        print("="*60)
        
        for endpoint in self.auth_endpoints:
            if endpoint['method'] == 'POST' and endpoint['post_data']:
                post_data = endpoint['post_data']
                
                if 'text' in post_data:
                    text = post_data['text']
                    
                    # Tìm email/password pattern
                    if any(keyword in text.lower() for keyword in ['email', 'password', 'username']):
                        print(f"\n🔍 Tìm thấy login pattern:")
                        print(f"URL: {endpoint['url']}")
                        print(f"Method: {endpoint['method']}")
                        print(f"Content-Type: {post_data.get('mimeType', 'N/A')}")
                        print(f"Data: {text}")
                        
                        # Parse JSON nếu có thể
                        try:
                            json_data = json.loads(text)
                            print(f"Parsed JSON:")
                            for key, value in json_data.items():
                                if 'password' in key.lower():
                                    print(f"  {key}: [HIDDEN]")
                                else:
                                    print(f"  {key}: {value}")
                        except:
                            pass
    
    def generate_summary_report(self):
        """
        Tạo báo cáo tổng kết
        """
        print("\n" + "="*60)
        print("BÁO CÁO TỔNG KẾT")
        print("="*60)
        
        print(f"📊 Tổng số requests: {len(self.entries)}")
        print(f"🔐 Số endpoints auth: {len(self.auth_endpoints)}")
        
        # Thống kê methods
        methods = defaultdict(int)
        for endpoint in self.auth_endpoints:
            methods[endpoint['method']] += 1
        
        print(f"\n📈 Thống kê HTTP Methods (Auth endpoints):")
        for method, count in methods.items():
            print(f"  {method}: {count}")
        
        # Thống kê status codes
        status_codes = defaultdict(int)
        for endpoint in self.auth_endpoints:
            status_codes[endpoint['status']] += 1
        
        print(f"\n📊 Thống kê Status Codes (Auth endpoints):")
        for status, count in status_codes.items():
            print(f"  {status}: {count}")
    
    def run_analysis(self):
        """
        Chạy toàn bộ quá trình phân tích
        """
        print("🚀 BẮT ĐẦU PHÂN TÍCH FILE HAR VBEE")
        print("="*60)
        
        if not self.load_har_file():
            return False
        
        # Phân tích domains
        self.analyze_domains()
        
        # Tìm auth endpoints
        self.find_auth_endpoints()
        
        # Phân tích login flow
        self.analyze_login_flow()
        
        # Tìm pattern đăng nhập
        self.extract_login_credentials_pattern()
        
        # Tạo báo cáo tổng kết
        self.generate_summary_report()
        
        return True

def main():
    """
    Hàm main để chạy analyzer
    """
    har_file = "studio.vbee.vn.har"
    
    analyzer = VbeeHARAnalyzer(har_file)
    
    if analyzer.run_analysis():
        print("\n✅ Phân tích hoàn tất!")
        
        # Lưu kết quả vào file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"vbee_analysis_{timestamp}.txt"
        
        print(f"\n💾 Kết quả đã được lưu vào: {output_file}")
    else:
        print("\n❌ Phân tích thất bại!")

if __name__ == "__main__":
    main()
