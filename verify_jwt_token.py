#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để verify JWT token từ Vbee
"""

import base64
import json
import sys

def decode_jwt_payload(token):
    """
    Decode JWT payload (không verify signature)
    
    Args:
        token: JWT token string
        
    Returns:
        dict: Decoded payload
    """
    try:
        # JWT có 3 phần: header.payload.signature
        parts = token.split('.')
        if len(parts) != 3:
            raise ValueError("Invalid JWT format")
        
        # Decode payload (phần thứ 2)
        payload = parts[1]
        
        # Thêm padding nếu cần
        padding = 4 - len(payload) % 4
        if padding != 4:
            payload += '=' * padding
        
        # Decode base64
        decoded_bytes = base64.urlsafe_b64decode(payload)
        decoded_json = json.loads(decoded_bytes.decode('utf-8'))
        
        return decoded_json
        
    except Exception as e:
        print(f"❌ Lỗi decode JWT: {e}")
        return None

def verify_token_from_file(filename):
    """
    Verify token từ file kết quả
    
    Args:
        filename: Tên file chứa kết quả
    """
    print(f"🔍 VERIFY JWT TOKEN TỪ FILE: {filename}")
    print("="*60)
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and '|' in line:
                email, token = line.split('|', 1)
                
                if token.startswith('ERROR:'):
                    print(f"❌ {email}: {token}")
                    continue
                
                print(f"\n📧 Email: {email}")
                print(f"🎫 Token: {token[:50]}...")
                
                # Decode JWT payload
                payload = decode_jwt_payload(token)
                if payload:
                    print(f"✅ JWT Payload:")
                    print(f"   👤 Subject: {payload.get('sub', 'N/A')}")
                    print(f"   📧 Email: {payload.get('email', 'N/A')}")
                    print(f"   👤 Username: {payload.get('username', 'N/A')}")
                    print(f"   📛 Name: {payload.get('name', 'N/A')}")
                    print(f"   📱 Phone: {payload.get('phone_number', 'N/A')}")
                    print(f"   🏢 Client: {payload.get('azp', 'N/A')}")
                    
                    # Thời gian
                    import datetime
                    if 'iat' in payload:
                        iat = datetime.datetime.fromtimestamp(payload['iat'])
                        print(f"   🕐 Issued At: {iat}")
                    
                    if 'exp' in payload:
                        exp = datetime.datetime.fromtimestamp(payload['exp'])
                        print(f"   ⏰ Expires At: {exp}")
                        
                        # Kiểm tra còn hạn không
                        now = datetime.datetime.now()
                        if exp > now:
                            remaining = exp - now
                            print(f"   ✅ Token còn hạn: {remaining}")
                        else:
                            print(f"   ❌ Token đã hết hạn")
                else:
                    print(f"❌ Không thể decode JWT payload")
                
                print("-" * 60)
                
    except FileNotFoundError:
        print(f"❌ Không tìm thấy file: {filename}")
    except Exception as e:
        print(f"❌ Lỗi đọc file: {e}")

def test_token_with_api(token):
    """
    Test token với Vbee API
    
    Args:
        token: JWT token
    """
    import requests
    
    print(f"\n🧪 TEST TOKEN VỚI VBEE API")
    print("="*40)
    
    # Test với API profile
    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        # Thử gọi API user profile
        profile_url = "https://accounts.vbee.vn/api/v1/user/profile"
        response = requests.get(profile_url, headers=headers)
        
        print(f"📡 Profile API - Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                profile_data = response.json()
                print(f"✅ Profile data: {json.dumps(profile_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"✅ Profile response: {response.text}")
        else:
            print(f"❌ Profile API failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Lỗi test API: {e}")

def main():
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = "test_result_v4.txt"
    
    verify_token_from_file(filename)
    
    # Nếu có token thành công, test với API
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and '|' in line:
                email, token = line.split('|', 1)
                
                if not token.startswith('ERROR:'):
                    test_token_with_api(token)
                    break
                    
    except Exception as e:
        print(f"❌ Lỗi test API: {e}")

if __name__ == "__main__":
    main()
