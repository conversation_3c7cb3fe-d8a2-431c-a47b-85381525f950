#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script để test login response thực tế
"""

import requests
import json
import hashlib
import base64
import secrets
import re
from urllib.parse import urlparse, parse_qs

def generate_pkce_params():
    """Tạo PKCE parameters"""
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    challenge_bytes = hashlib.sha256(code_verifier.encode('utf-8')).digest()
    code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
    return code_verifier, code_challenge

def test_login_flow():
    """Test toàn bộ login flow"""
    print("🧪 TESTING VBEE LOGIN FLOW")
    print("="*50)
    
    # Setup session
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    })
    
    # Tạo PKCE params
    code_verifier, code_challenge = generate_pkce_params()
    print(f"🔑 Code Verifier: {code_verifier[:20]}...")
    print(f"🔑 Code Challenge: {code_challenge}")
    
    # Bước 1: OAuth authorize
    print(f"\n📍 BƯỚC 1: OAuth Authorize")
    authorize_params = {
        'clientId': 'aivoice-web-application',
        'codeChallenge': code_challenge,
        'codeChallengeMethod': 'S256',
        'redirectUri': 'https://studio.vbee.vn/studio/text-to-speech?',
        'responseType': 'code',
        'action': 'login'
    }
    
    authorize_url = "https://auth.vbee.vn/authorize"
    response = session.get(authorize_url, params=authorize_params, allow_redirects=False)
    
    print(f"Status: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    
    session_id = None
    if response.status_code == 307:
        location = response.headers.get('Location', '')
        print(f"Redirect to: {location}")
        
        session_match = re.search(r'session=([^&]+)', location)
        if session_match:
            session_id = session_match.group(1)
            print(f"✅ Session ID: {session_id}")
        else:
            print("❌ Không tìm thấy session ID")
            return
    else:
        print("❌ Không có redirect 307")
        return
    
    # Bước 2: Login
    print(f"\n📍 BƯỚC 2: Login")
    login_url = "https://accounts.vbee.vn/api/v1/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "thach111a",
        "session": session_id
    }
    
    login_headers = {
        'Content-Type': 'application/json',
        'Referer': 'https://auth.vbee.vn/'
    }
    
    response = session.post(login_url, json=login_data, headers=login_headers)
    
    print(f"Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    try:
        response_json = response.json()
        print(f"Response JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
    except:
        print(f"Response Text: {response.text}")
    
    # Bước 3: Kiểm tra có redirect URL trong response không
    if response.status_code == 200:
        try:
            response_data = response.json()
            if 'redirectUrl' in response_data:
                redirect_url = response_data['redirectUrl']
                print(f"🔄 Redirect URL từ response: {redirect_url}")
                
                # Extract code
                code_match = re.search(r'code=([^&]+)', redirect_url)
                if code_match:
                    auth_code = code_match.group(1)
                    print(f"✅ Authorization Code: {auth_code}")
                    
                    # Test token exchange
                    test_token_exchange(session, auth_code, code_verifier)
                    return
        except:
            pass
    
    # Bước 4: Thử truy cập redirect URI
    print(f"\n📍 BƯỚC 3: Kiểm tra Redirect URI")
    redirect_uri = "https://studio.vbee.vn/studio/text-to-speech?"
    
    response = session.get(redirect_uri, allow_redirects=True)
    print(f"Final URL: {response.url}")
    print(f"Status: {response.status_code}")
    
    # Kiểm tra URL có code không
    code_match = re.search(r'code=([^&]+)', response.url)
    if code_match:
        auth_code = code_match.group(1)
        print(f"✅ Authorization Code từ URL: {auth_code}")
        
        # Test token exchange
        test_token_exchange(session, auth_code, code_verifier)
    else:
        print("❌ Không tìm thấy authorization code trong URL")
        
        # Thử refresh-token endpoint
        test_refresh_token(session)

def test_token_exchange(session, auth_code, code_verifier):
    """Test token exchange"""
    print(f"\n📍 BƯỚC 4: Token Exchange")
    
    token_url = "https://accounts.vbee.vn/api/v1/auth/token"
    token_data = {
        "grant_type": "authorization_code",
        "client_id": "aivoice-web-application",
        "code": auth_code,
        "code_verifier": code_verifier,
        "redirect_uri": "https://studio.vbee.vn/studio/text-to-speech?"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Referer': 'https://studio.vbee.vn/'
    }
    
    response = session.post(token_url, json=token_data, headers=headers)
    
    print(f"Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    try:
        response_json = response.json()
        print(f"Response JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        
        # Tìm token
        if 'result' in response_json and 'access_token' in response_json['result']:
            token = response_json['result']['access_token']
            print(f"✅ Access Token: {token[:50]}...")
        elif 'access_token' in response_json:
            token = response_json['access_token']
            print(f"✅ Access Token: {token[:50]}...")
        else:
            print("❌ Không tìm thấy access token")
    except:
        print(f"Response Text: {response.text}")

def test_refresh_token(session):
    """Test refresh token endpoint"""
    print(f"\n📍 BƯỚC 5: Test Refresh Token")
    
    refresh_url = "https://accounts.vbee.vn/api/v1/auth/refresh-token"
    refresh_data = {
        "clientId": "aivoice-web-application"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Referer': 'https://studio.vbee.vn/'
    }
    
    response = session.post(refresh_url, json=refresh_data, headers=headers)
    
    print(f"Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    try:
        response_json = response.json()
        print(f"Response JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        
        # Tìm token
        if 'result' in response_json and 'access_token' in response_json['result']:
            token = response_json['result']['access_token']
            print(f"✅ Access Token từ refresh: {token[:50]}...")
        elif 'access_token' in response_json:
            token = response_json['access_token']
            print(f"✅ Access Token từ refresh: {token[:50]}...")
        else:
            print("❌ Không tìm thấy access token trong refresh response")
    except:
        print(f"Response Text: {response.text}")

if __name__ == "__main__":
    test_login_flow()
