# Vbee Auto Login Tool V2 🚀

**Clean Architecture với Multi-threading Support**

Tool tự động đăng nhập và lấy JWT token từ Vbee với kiến trúc sạch, xử lý đa luồng thông minh và error handling nâng cao.

## ✨ Tính năng mới V2

### 🏗️ Clean Architecture
- **Separation of Concerns**: Tách biệt rõ ràng các thành phần
- **SOLID Principles**: <PERSON><PERSON> thủ nguyên tắc thiết kế phần mềm
- **Dependency Injection**: Dễ dàng test và mở rộng
- **Interface-based Design**: Linh hoạt và có thể thay thế

### ⚡ Multi-threading Support
- **ThreadPoolExecutor**: Xử lý nhiều account song song
- **Intelligent Rate Limiting**: Tự động điều chỉnh tốc độ request
- **Connection Pooling**: T<PERSON>i sử dụng HTTP connections hiệu quả
- **Thread-safe Operations**: An toàn trong môi trường đa luồng

### 🛡️ Enhanced Error Handling
- **Custom Exception Classes**: Phân loại lỗi chi tiết
- **Retry Mechanism**: Tự động thử lại với exponential backoff
- **Thread-safe Logging**: Ghi log an toàn trong đa luồng
- **Graceful Degradation**: Xử lý lỗi mượt mà

### 📊 Performance Optimization
- **Batch Processing**: Xử lý hàng loạt hiệu quả
- **Resource Management**: Quản lý tài nguyên thông minh
- **Progress Tracking**: Theo dõi tiến độ real-time
- **Memory Efficient**: Tối ưu sử dụng bộ nhớ

## 🏛️ Kiến trúc hệ thống

```
vbee_auto_login_v2.py          # Main application
├── vbee_auth_client.py        # Authentication client
├── vbee_batch_processor.py    # Multi-threaded batch processor
├── vbee_exceptions.py         # Custom exception classes
└── config.json               # Configuration file
```

### 📦 Các thành phần chính

#### 1. **VbeeAuthClient** - Authentication Core
```python
# Single Responsibility: Xử lý authentication cho 1 account
class VbeeAuthClient:
    def authenticate(username, password) -> AuthResult
```

#### 2. **VbeeBatchProcessor** - Multi-threading Engine
```python
# Xử lý nhiều account song song với rate limiting
class VbeeBatchProcessor:
    def process_accounts(accounts) -> List[AuthResult]
```

#### 3. **VbeeOAuthFlow** - OAuth Handler
```python
# Xử lý OAuth 2.0 PKCE flow
class VbeeOAuthFlow:
    def start_oauth_flow() -> bool
    def login_with_credentials() -> str
    def get_access_token() -> str
```

#### 4. **Exception System** - Error Management
```python
# Hệ thống exception phân cấp
VbeeException
├── AuthenticationError
├── NetworkError
├── APIError
├── ConfigurationError
└── ProcessingError
```

## 🚀 Cách sử dụng

### Command Line Interface

```bash
# Cơ bản
python vbee_auto_login_v2.py --accounts accounts.txt

# Với tùy chọn nâng cao
python vbee_auto_login_v2.py \
    --accounts accounts.txt \
    --output results.txt \
    --workers 5 \
    --rate-limit 2.0 \
    --config custom_config.json
```

### Tham số

| Tham số | Mô tả | Mặc định |
|---------|-------|----------|
| `--accounts` | File chứa danh sách account | **Bắt buộc** |
| `--output` | File lưu kết quả | Auto-generated |
| `--workers` | Số luồng xử lý | 5 |
| `--rate-limit` | Giới hạn request/giây | 2.0 |
| `--config` | File cấu hình | config.json |

### Programmatic Usage

```python
from vbee_auto_login_v2 import VbeeAutoLoginV2
from vbee_batch_processor import BatchConfig, Account

# Khởi tạo
app = VbeeAutoLoginV2()

# Xử lý single account
result = app.process_single_account("<EMAIL>", "password")

# Xử lý batch accounts
results = app.process_accounts_file("accounts.txt")
```

## ⚙️ Cấu hình

### config.json
```json
{
  "timeout": 30,
  "retry_count": 3,
  "delay_between_requests": 2,
  "delay_between_accounts": 1,
  "user_agent": "Mozilla/5.0...",
  "client_id": "aivoice-web-application",
  "redirect_uri": "https://studio.vbee.vn/studio/text-to-speech?",
  "base_urls": {
    "auth": "https://auth.vbee.vn",
    "accounts": "https://accounts.vbee.vn",
    "studio": "https://studio.vbee.vn"
  },
  "batch": {
    "max_workers": 5,
    "rate_limit_per_second": 2.0,
    "delay_between_accounts": 1.0,
    "delay_between_requests": 2.0,
    "retry_count": 3,
    "timeout": 30
  }
}
```

### Batch Configuration
```python
BatchConfig(
    max_workers=5,              # Số luồng tối đa
    rate_limit_per_second=2.0,  # Request/giây
    delay_between_accounts=1.0, # Delay giữa các account
    retry_count=3,              # Số lần thử lại
    timeout=30                  # Timeout cho mỗi request
)
```

## 📊 Kết quả test

### Performance Comparison

| Metric | V1 (Sequential) | V2 (Multi-threaded) | Improvement |
|--------|-----------------|---------------------|-------------|
| **10 accounts** | ~50 seconds | ~18 seconds | **64% faster** |
| **Success Rate** | 100% | 100% | Maintained |
| **Memory Usage** | Low | Optimized | Efficient |
| **Error Handling** | Basic | Advanced | Enhanced |

### Test Results V2

```
🚀 VBEE AUTO LOGIN TOOL V2
==================================================
📖 Accounts file: accounts_multi_test.txt
💾 Output file: test_multi_result.txt
⚙️ Config file: config.json
👥 Workers: 5
⚡ Rate limit: 1.5/sec
==================================================

📈 Progress: 10/10 (100.0%) - Rate: 0.6/sec - ETA: 0s
💾 Results saved to: test_multi_result.txt

============================================================
📊 BATCH PROCESSING SUMMARY
============================================================
✅ Successful: 10/10 accounts
❌ Failed: 0/10 accounts

⏱️ Total processing time: 17.89 seconds
✅ Processing completed!
```

## 🔧 Tính năng nâng cao

### 1. **Intelligent Rate Limiting**
```python
class RateLimiter:
    def __init__(self, max_calls_per_second: float):
        self.max_calls_per_second = max_calls_per_second
        self.min_interval = 1.0 / max_calls_per_second
        self.lock = threading.Lock()
```

### 2. **Connection Pooling**
```python
class HttpClientPool:
    def __init__(self, pool_size: int):
        self.pool = Queue(maxsize=pool_size)
        # Pre-initialize HTTP clients
```

### 3. **Progress Tracking**
```python
class ProgressTracker:
    def update(self, completed: int, total: int):
        # Real-time progress with ETA calculation
```

### 4. **Thread-safe Logging**
```python
class ThreadSafeLogger:
    def _log(self, level: str, message: str):
        with self.lock:
            thread_id = threading.current_thread().ident
            formatted_message = f"[Thread-{thread_id}] {message}"
```

## 🛠️ Development

### Requirements
```
requests>=2.31.0
```

### Testing
```bash
# Test single account
python vbee_auto_login_v2.py --accounts accounts_example.txt

# Test multi-threading
python vbee_auto_login_v2.py --accounts accounts_multi_test.txt --workers 5

# Test with custom rate limit
python vbee_auto_login_v2.py --accounts accounts.txt --rate-limit 1.0
```

### Code Quality
- **Clean Architecture**: SOLID principles
- **Type Hints**: Full type annotation
- **Error Handling**: Comprehensive exception system
- **Documentation**: Detailed docstrings
- **Thread Safety**: All operations are thread-safe

## 📈 Monitoring & Logging

### Log Files
- `vbee_login_v2_YYYYMMDD_HHMMSS.log` - Detailed logs
- Console output - Real-time progress

### Log Levels
- **INFO**: Progress và status updates
- **ERROR**: Lỗi và exceptions
- **DEBUG**: Chi tiết technical (file only)

### Metrics Tracked
- Processing rate (accounts/second)
- Success/failure ratios
- Thread utilization
- Network performance
- Error categorization

## 🔮 Future Enhancements

- [ ] **Async/Await Support**: Python asyncio integration
- [ ] **Database Integration**: Store results in database
- [ ] **Web Dashboard**: Real-time monitoring interface
- [ ] **API Server**: RESTful API endpoints
- [ ] **Docker Support**: Containerized deployment
- [ ] **Kubernetes**: Scalable cloud deployment

## 📝 Changelog V2

### ✅ Completed Features
- ✅ Clean Architecture với SOLID principles
- ✅ Multi-threading với ThreadPoolExecutor
- ✅ Intelligent rate limiting
- ✅ Enhanced error handling system
- ✅ Connection pooling
- ✅ Thread-safe logging
- ✅ Progress tracking với ETA
- ✅ Batch processing optimization
- ✅ Resource management
- ✅ Performance monitoring

### 🎯 Key Improvements
1. **64% faster** processing với multi-threading
2. **100% success rate** maintained
3. **Advanced error handling** với custom exceptions
4. **Thread-safe operations** cho stability
5. **Intelligent resource management**
6. **Real-time progress tracking**

---

**Vbee Auto Login Tool V2** - *Xử lý mượt mà, code thông minh và khoa học* ✨
