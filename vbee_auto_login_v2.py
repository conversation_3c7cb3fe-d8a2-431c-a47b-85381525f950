#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vbee Auto Login Tool V2
Clean architecture with multi-threading support
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

from vbee_auth_client import AuthStatus
from vbee_batch_processor import (
    VbeeBatchProcessor, BatchConfig, Account, AccountFileReader, AuthResult
)


class ConfigManager:
    """Configuration manager with validation"""
    
    @staticmethod
    def load_config(config_file: str = "config.json") -> Dict:
        """Load configuration with defaults"""
        default_config = {
            "timeout": 30,
            "retry_count": 3,
            "delay_between_requests": 2,
            "delay_between_accounts": 1,
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "client_id": "aivoice-web-application",
            "redirect_uri": "https://studio.vbee.vn/studio/text-to-speech?",
            "base_urls": {
                "auth": "https://auth.vbee.vn",
                "accounts": "https://accounts.vbee.vn",
                "studio": "https://studio.vbee.vn"
            },
            "batch": {
                "max_workers": 5,
                "rate_limit_per_second": 2.0,
                "delay_between_accounts": 1.0,
                "delay_between_requests": 2.0,
                "retry_count": 3,
                "timeout": 30
            }
        }
        
        try:
            if Path(config_file).exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # Merge with defaults
                ConfigManager._deep_merge(default_config, user_config)
            else:
                # Create default config file
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                print(f"📝 Created default config file: {config_file}")
            
            return default_config
            
        except Exception as e:
            print(f"❌ Error loading config: {e}")
            return default_config
    
    @staticmethod
    def _deep_merge(base_dict: Dict, update_dict: Dict):
        """Deep merge dictionaries"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                ConfigManager._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value


class LoggerSetup:
    """Logger setup utility"""
    
    @staticmethod
    def setup_logger(name: str = "VbeeAutoLoginV2") -> logging.Logger:
        """Setup logger with file and console handlers"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        simple_formatter = logging.Formatter('%(message)s')
        
        # File handler
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"vbee_login_v2_{timestamp}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        
        # Add handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger


class ResultsManager:
    """Manages saving and displaying results"""
    
    @staticmethod
    def save_results(results: List[AuthResult], output_file: str):
        """Save results to file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # Header
                f.write("# Vbee Auto Login Results V2\n")
                f.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("# Format: email|jwt_token_or_error\n\n")
                
                # Results
                success_count = 0
                for result in results:
                    if result.status == AuthStatus.SUCCESS:
                        f.write(f"{result.username}|{result.token}\n")
                        success_count += 1
                    else:
                        f.write(f"{result.username}|ERROR: {result.error_message}\n")
                
                # Summary
                f.write(f"\n# Summary: {success_count}/{len(results)} successful logins\n")
            
            print(f"💾 Results saved to: {output_file}")
            
        except Exception as e:
            print(f"❌ Error saving results: {e}")
    
    @staticmethod
    def print_summary(results: List[AuthResult]):
        """Print results summary"""
        if not results:
            print("❌ No results to display")
            return
        
        success_results = [r for r in results if r.status == AuthStatus.SUCCESS]
        failed_results = [r for r in results if r.status != AuthStatus.SUCCESS]
        
        print("\n" + "="*60)
        print("📊 BATCH PROCESSING SUMMARY")
        print("="*60)
        print(f"✅ Successful: {len(success_results)}/{len(results)} accounts")
        print(f"❌ Failed: {len(failed_results)}/{len(results)} accounts")
        
        if success_results:
            print(f"\n🎯 SUCCESSFUL ACCOUNTS:")
            for result in success_results:
                token_preview = result.token[:50] + "..." if result.token and len(result.token) > 50 else result.token
                print(f"   ✅ {result.username} - Token: {token_preview}")
        
        if failed_results:
            print(f"\n❌ FAILED ACCOUNTS:")
            for result in failed_results:
                print(f"   ❌ {result.username} - {result.error_message}")
        
        print("="*60)


class ProgressTracker:
    """Progress tracking utility"""
    
    def __init__(self, total: int):
        self.total = total
        self.start_time = time.time()
        self.last_update = 0
    
    def update(self, completed: int, total: int):
        """Update progress"""
        if completed - self.last_update >= 1 or completed == total:
            elapsed = time.time() - self.start_time
            rate = completed / elapsed if elapsed > 0 else 0
            eta = (total - completed) / rate if rate > 0 else 0
            
            progress_percent = (completed / total) * 100
            print(f"📈 Progress: {completed}/{total} ({progress_percent:.1f}%) - "
                  f"Rate: {rate:.1f}/sec - ETA: {eta:.0f}s")
            
            self.last_update = completed


class VbeeAutoLoginV2:
    """
    Main application class for Vbee Auto Login V2
    Clean architecture with multi-threading support
    """
    
    def __init__(self, config_file: str = "config.json"):
        self.config = ConfigManager.load_config(config_file)
        self.logger = LoggerSetup.setup_logger()
        
        # Create batch config
        batch_config_data = self.config.get('batch', {})
        self.batch_config = BatchConfig(
            max_workers=batch_config_data.get('max_workers', 5),
            delay_between_accounts=batch_config_data.get('delay_between_accounts', 1.0),
            delay_between_requests=batch_config_data.get('delay_between_requests', 2.0),
            retry_count=batch_config_data.get('retry_count', 3),
            rate_limit_per_second=batch_config_data.get('rate_limit_per_second', 2.0),
            timeout=batch_config_data.get('timeout', 30)
        )
        
        self.logger.info("🚀 Vbee Auto Login V2 initialized")
        self.logger.info(f"⚙️ Batch config: {self.batch_config.max_workers} workers, "
                        f"{self.batch_config.rate_limit_per_second} req/sec")
    
    def process_accounts_file(self, accounts_file: str, output_file: str = None) -> List[AuthResult]:
        """
        Process accounts from file with multi-threading
        
        Args:
            accounts_file: Path to accounts file
            output_file: Optional output file path
            
        Returns:
            List[AuthResult]: Processing results
        """
        # Read accounts
        self.logger.info(f"📖 Reading accounts from: {accounts_file}")
        accounts = AccountFileReader.read_accounts(accounts_file)
        
        if not accounts:
            self.logger.error("❌ No valid accounts found")
            return []
        
        # Create output file name if not provided
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"vbee_tokens_v2_{timestamp}.txt"
        
        # Setup progress tracking
        progress_tracker = ProgressTracker(len(accounts))
        
        # Process accounts
        processor = VbeeBatchProcessor(self.config, self.batch_config)
        
        try:
            self.logger.info(f"🚀 Starting batch processing for {len(accounts)} accounts")
            
            results = processor.process_accounts(
                accounts, 
                progress_callback=progress_tracker.update
            )
            
            # Save results
            ResultsManager.save_results(results, output_file)
            
            # Print summary
            ResultsManager.print_summary(results)
            
            return results
            
        finally:
            processor.close()
    
    def process_single_account(self, username: str, password: str) -> AuthResult:
        """
        Process single account
        
        Args:
            username: Email for login
            password: Password
            
        Returns:
            AuthResult: Processing result
        """
        account = Account(username=username, password=password)
        processor = VbeeBatchProcessor(self.config, self.batch_config)
        
        try:
            results = processor.process_accounts([account])
            return results[0] if results else AuthResult(
                status=AuthStatus.FAILED,
                username=username,
                error_message="No result returned"
            )
        finally:
            processor.close()


def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Vbee Auto Login Tool V2 - Multi-threaded with clean architecture'
    )
    parser.add_argument('--accounts', '-a', required=True, 
                       help='File containing account list (email|password)')
    parser.add_argument('--output', '-o', 
                       help='Output file to save results')
    parser.add_argument('--config', '-c', default='config.json', 
                       help='Configuration file')
    parser.add_argument('--workers', '-w', type=int, 
                       help='Number of worker threads (overrides config)')
    parser.add_argument('--rate-limit', '-r', type=float, 
                       help='Rate limit per second (overrides config)')
    
    args = parser.parse_args()
    
    print("🚀 VBEE AUTO LOGIN TOOL V2")
    print("="*50)
    print(f"📖 Accounts file: {args.accounts}")
    print(f"💾 Output file: {args.output or 'auto-generated'}")
    print(f"⚙️ Config file: {args.config}")
    if args.workers:
        print(f"👥 Workers: {args.workers}")
    if args.rate_limit:
        print(f"⚡ Rate limit: {args.rate_limit}/sec")
    print("="*50)
    
    # Initialize application
    app = VbeeAutoLoginV2(args.config)
    
    # Override config if specified
    if args.workers:
        app.batch_config.max_workers = args.workers
    if args.rate_limit:
        app.batch_config.rate_limit_per_second = args.rate_limit
    
    # Process accounts
    start_time = time.time()
    results = app.process_accounts_file(args.accounts, args.output)
    elapsed_time = time.time() - start_time
    
    print(f"\n⏱️ Total processing time: {elapsed_time:.2f} seconds")
    print("✅ Processing completed!")


if __name__ == "__main__":
    main()
