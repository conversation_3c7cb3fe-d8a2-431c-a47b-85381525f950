#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vbee Authentication Client
Clean architecture implementation with separation of concerns
"""

import requests
import hashlib
import base64
import secrets
import urllib.parse
import re
import json
import time
from typing import Dict, <PERSON>tional, <PERSON><PERSON>
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

from vbee_exceptions import (
    VbeeException, AuthenticationError, NetworkError, APIError,
    ExceptionHandler, ErrorCode, is_retryable_error
)


class AuthStatus(Enum):
    """Authentication status enumeration"""
    SUCCESS = "success"
    FAILED = "failed"
    RETRY_NEEDED = "retry_needed"
    RATE_LIMITED = "rate_limited"


@dataclass
class AuthResult:
    """Authentication result data class"""
    status: AuthStatus
    username: str
    token: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0


@dataclass
class OAuthParams:
    """OAuth PKCE parameters"""
    code_verifier: str
    code_challenge: str
    session_id: Optional[str] = None
    refresh_token: Optional[str] = None


class IHttpClient(ABC):
    """HTTP Client interface for dependency injection"""
    
    @abstractmethod
    def get(self, url: str, **kwargs) -> requests.Response:
        pass
    
    @abstractmethod
    def post(self, url: str, **kwargs) -> requests.Response:
        pass


class ILogger(ABC):
    """Logger interface for dependency injection"""
    
    @abstractmethod
    def info(self, message: str) -> None:
        pass
    
    @abstractmethod
    def error(self, message: str) -> None:
        pass
    
    @abstractmethod
    def warning(self, message: str) -> None:
        pass
    
    @abstractmethod
    def debug(self, message: str) -> None:
        pass


class HttpClient(IHttpClient):
    """HTTP Client implementation with session management"""
    
    def __init__(self, timeout: int = 30, user_agent: str = None):
        self.session = requests.Session()
        self.timeout = timeout
        
        if user_agent:
            self.session.headers.update({'User-Agent': user_agent})
    
    def get(self, url: str, **kwargs) -> requests.Response:
        kwargs.setdefault('timeout', self.timeout)
        return self.session.get(url, **kwargs)
    
    def post(self, url: str, **kwargs) -> requests.Response:
        kwargs.setdefault('timeout', self.timeout)
        return self.session.post(url, **kwargs)
    
    def close(self):
        """Close session"""
        self.session.close()


class PKCEGenerator:
    """PKCE (Proof Key for Code Exchange) parameter generator"""
    
    @staticmethod
    def generate_code_verifier() -> str:
        """Generate code verifier for PKCE"""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    @staticmethod
    def generate_code_challenge(code_verifier: str) -> str:
        """Generate code challenge from verifier"""
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')
    
    @classmethod
    def generate_params(cls) -> OAuthParams:
        """Generate complete PKCE parameters"""
        code_verifier = cls.generate_code_verifier()
        code_challenge = cls.generate_code_challenge(code_verifier)
        return OAuthParams(code_verifier=code_verifier, code_challenge=code_challenge)


class VbeeOAuthFlow:
    """Handles Vbee OAuth 2.0 PKCE flow"""
    
    def __init__(self, http_client: IHttpClient, logger: ILogger, config: Dict):
        self.http_client = http_client
        self.logger = logger
        self.config = config
        self.oauth_params: Optional[OAuthParams] = None
    
    def start_oauth_flow(self) -> bool:
        """
        Initialize OAuth authorization flow
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info("🚀 Starting OAuth authorization flow...")
            
            # Generate PKCE parameters
            self.oauth_params = PKCEGenerator.generate_params()
            
            # Create authorize URL
            params = {
                'clientId': self.config['client_id'],
                'codeChallenge': self.oauth_params.code_challenge,
                'codeChallengeMethod': 'S256',
                'redirectUri': self.config['redirect_uri'],
                'responseType': 'code',
                'action': 'login'
            }
            
            authorize_url = f"{self.config['base_urls']['auth']}/authorize"
            
            # Send authorize request
            response = self.http_client.get(authorize_url, params=params, allow_redirects=False)
            
            self.logger.info(f"📡 Authorize request - Status: {response.status_code}")
            
            if response.status_code == 307:
                # Extract session ID from redirect location
                location = response.headers.get('Location', '')
                session_match = re.search(r'session=([^&]+)', location)
                
                if session_match:
                    self.oauth_params.session_id = session_match.group(1)
                    self.logger.info(f"✅ Session ID obtained: {self.oauth_params.session_id}")
                    return True
                else:
                    self.logger.error("❌ Session ID not found in redirect URL")
                    return False
            else:
                self.logger.error(f"❌ Authorize request failed - Status: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            vbee_error = ExceptionHandler.handle_requests_exception(e, authorize_url)
            self.logger.error(f"❌ Network error in OAuth flow: {vbee_error}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error in OAuth flow: {e}")
            return False
    
    def login_with_credentials(self, username: str, password: str) -> Optional[str]:
        """
        Login with username/password and get authorization code
        
        Args:
            username: Email for login
            password: Password
            
        Returns:
            str: Authorization code if successful, None if failed
        """
        try:
            self.logger.info(f"🔑 Logging in with username: {username}")
            
            if not self.oauth_params or not self.oauth_params.session_id:
                self.logger.error("❌ No session ID, need to run OAuth flow first")
                return None
            
            # Prepare login request
            login_url = f"{self.config['base_urls']['accounts']}/api/v1/login"
            login_data = {
                "username": username,
                "password": password,
                "session": self.oauth_params.session_id
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Referer': f"{self.config['base_urls']['auth']}/"
            }
            
            # Send login request
            response = self.http_client.post(login_url, json=login_data, headers=headers)
            
            self.logger.info(f"📡 Login request - Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    
                    # Check if response contains authorization code directly
                    if 'result' in response_data and 'code' in response_data['result']:
                        auth_code = response_data['result']['code']
                        self.logger.info(f"✅ Authorization code obtained from response: {auth_code}")
                        
                        # Save refresh token if available
                        if 'refresh_token' in response_data['result']:
                            self.oauth_params.refresh_token = response_data['result']['refresh_token']
                            self.logger.info("✅ Refresh token saved")
                        
                        return auth_code
                    
                except json.JSONDecodeError:
                    self.logger.warning("⚠️ Response is not JSON, but status 200")
            
            self.logger.error(f"❌ Login failed - Status: {response.status_code}")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error during login: {e}")
            return None
    
    def get_access_token(self, auth_code: str) -> Optional[str]:
        """
        Get access token using authorization code or refresh token
        
        Args:
            auth_code: Authorization code from OAuth flow
            
        Returns:
            str: Access token if successful, None if failed
        """
        try:
            self.logger.info("🎯 Exchanging authorization code for access token...")
            
            # Try refresh token endpoint with refresh token
            if self.oauth_params and self.oauth_params.refresh_token:
                return self._try_refresh_token_endpoint()
            
            self.logger.error("❌ No refresh token available")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error getting access token: {e}")
            return None
    
    def _try_refresh_token_endpoint(self) -> Optional[str]:
        """Try to get token from refresh-token endpoint with refresh token"""
        try:
            self.logger.info("🔄 Trying refresh-token endpoint with refresh token...")
            
            if not self.oauth_params.refresh_token:
                self.logger.error("❌ No refresh token available")
                return None
            
            refresh_url = f"{self.config['base_urls']['accounts']}/api/v1/auth/refresh-token"
            
            # Try with refresh token in body
            refresh_data = {
                "refresh_token": self.oauth_params.refresh_token,
                "clientId": self.config['client_id']
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Referer': self.config['base_urls']['studio'] + "/"
            }
            
            response = self.http_client.post(refresh_url, json=refresh_data, headers=headers)
            
            self.logger.info(f"📡 Refresh token request - Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    token_data = response.json()
                    
                    # Find access token in response
                    if 'result' in token_data and 'access_token' in token_data['result']:
                        access_token = token_data['result']['access_token']
                        self.logger.info("✅ Access token obtained from refresh-token endpoint!")
                        return access_token
                    
                except json.JSONDecodeError:
                    self.logger.error("❌ Refresh response is not JSON")
            
            self.logger.error(f"❌ Refresh token request failed - Status: {response.status_code}")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error in refresh token endpoint: {e}")
            return None


class VbeeAuthClient:
    """
    Clean Vbee Authentication Client
    Single Responsibility: Handle authentication for a single account
    """
    
    def __init__(self, http_client: IHttpClient, logger: ILogger, config: Dict):
        self.http_client = http_client
        self.logger = logger
        self.config = config
        self.oauth_flow = VbeeOAuthFlow(http_client, logger, config)
    
    def authenticate(self, username: str, password: str) -> AuthResult:
        """
        Authenticate a single account
        
        Args:
            username: Email for login
            password: Password
            
        Returns:
            AuthResult: Authentication result
        """
        try:
            self.logger.info(f"🎯 Starting authentication for: {username}")
            
            # Step 1: Initialize OAuth flow
            if not self.oauth_flow.start_oauth_flow():
                return AuthResult(
                    status=AuthStatus.FAILED,
                    username=username,
                    error_message="Failed to initialize OAuth flow"
                )
            
            # Delay between requests
            time.sleep(self.config.get('delay_between_requests', 2))
            
            # Step 2: Login with credentials and get authorization code
            auth_code = self.oauth_flow.login_with_credentials(username, password)
            if not auth_code:
                return AuthResult(
                    status=AuthStatus.FAILED,
                    username=username,
                    error_message="Login failed or authorization code not obtained"
                )
            
            # Delay between requests
            time.sleep(self.config.get('delay_between_requests', 2))
            
            # Step 3: Exchange authorization code for access token
            access_token = self.oauth_flow.get_access_token(auth_code)
            if not access_token:
                return AuthResult(
                    status=AuthStatus.FAILED,
                    username=username,
                    error_message="Failed to get access token"
                )
            
            self.logger.info(f"✅ Authentication successful for {username}")
            return AuthResult(
                status=AuthStatus.SUCCESS,
                username=username,
                token=access_token
            )
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"❌ Authentication error for {username}: {e}")
            return AuthResult(
                status=AuthStatus.FAILED,
                username=username,
                error_message=error_msg
            )
